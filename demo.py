#!/usr/bin/env python3
"""
DBLP作者信息查询演示程序

简洁的演示程序，展示如何使用DBLP API搜索作者并获取详细的Profile信息。

使用方法:
    python demo.py
"""

import warnings
import dblp
from dblp.parsers import DBLPParseWarning

# 抑制DBLP解析警告
warnings.filterwarnings('ignore', category=DBLPParseWarning)


def print_header(title: str, char: str = "=") -> None:
    """打印格式化的标题"""
    print(f"\n{char * 60}")
    print(f" {title}")
    print(f"{char * 60}")


def print_separator(char: str = "-") -> None:
    """打印分隔线"""
    print(char * 60)
def search_authors(query: str):
    """搜索作者"""
    print(f"\n🔍 正在搜索作者: '{query}'...")

    # 使用项目中的DBLP API搜索作者
    results = dblp.search_author(query)

    # 过滤掉None结果
    valid_results = [r for r in results if r is not None]

    if not valid_results:
        print("❌ 未找到匹配的作者")
        return None

    # 显示搜索结果
    print_header("搜索结果")
    for i, result in enumerate(valid_results, 1):
        author_name = result.get('author', 'Unknown Author')
        url = result.get('url', '')
        print(f"{i}. {author_name}")
        if url:
            print(f"   URL: {url}")
        print()

    # 如果只有一个结果，自动选择
    if len(valid_results) == 1:
        print("✅ 找到唯一匹配的作者，自动选择")
        return valid_results[0]

    # 让用户选择
    while True:
        choice = input(f"\n📝 请选择作者 (1-{len(valid_results)}): ").strip()
        if choice.isdigit():
            choice_num = int(choice)
            if 1 <= choice_num <= len(valid_results):
                return valid_results[choice_num - 1]
        print(f"⚠️  请输入 1 到 {len(valid_results)} 之间的数字")


def fetch_author_profile(author_info):
    """获取作者详细信息"""
    url = author_info.get('url', '')
    author_name = author_info.get('author', 'Unknown Author')

    print(f"\n📊 正在获取 {author_name} 的详细信息...")
    return dblp.fetch_author_profile(url)
def display_author_profile(profile):
    """显示作者详细信息"""
    print_header(f"作者档案: {profile.name}", "=")

    # 基本信息
    print(f"👤 姓名: {profile.name}")
    print(f"🆔 DBLP PID: {profile.pid}")
    print(f"📚 发表论文总数: {profile.publication_count}")
    print(f"📊 实际收录论文: {len(profile.publications)}")

    # 统计信息
    stats = profile.get_publication_stats()
    print_separator()
    print("📈 发表统计:")
    print(f"   期刊论文: {stats['journal_articles']}")
    print(f"   会议论文: {stats['conference_papers']}")
    print(f"   活跃年份: {stats['active_years']} 年")
    if stats['first_publication_year'] and stats['last_publication_year']:
        print(f"   发表时间跨度: {stats['first_publication_year']} - {stats['last_publication_year']}")
    print(f"   合作者数量: {stats['coauthors_count']}")
    print(f"   获奖数量: {stats['awards_count']}")

    # 机构信息
    if profile.affiliations:
        print_separator()
        print(f"🏛️  机构信息 ({len(profile.affiliations)}):")
        for i, affiliation in enumerate(profile.affiliations[:5], 1):
            print(f"   {i}. {affiliation}")
        if len(profile.affiliations) > 5:
            print(f"   ... 还有 {len(profile.affiliations) - 5} 个机构")

    # 获奖信息
    if profile.awards:
        print_separator()
        print(f"🏆 获奖信息 ({len(profile.awards)}):")
        for i, award in enumerate(profile.awards[:5], 1):
            year = award.get('year', 'Unknown')
            award_name = award.get('award', 'Unknown Award')
            print(f"   {i}. {year}: {award_name}")
        if len(profile.awards) > 5:
            print(f"   ... 还有 {len(profile.awards) - 5} 个奖项")

    # 近期论文
    recent_pubs = []
    for year in range(2020, 2026):  # 2020-2025
        year_pubs = profile.get_publications_by_year(year)
        recent_pubs.extend(year_pubs)

    if recent_pubs:
        print_separator()
        print(f"📝 近期论文 (2020-2025): {len(recent_pubs)} 篇")
        for i, pub in enumerate(recent_pubs[:5], 1):
            pub_type = "期刊" if hasattr(pub, 'journal') else "会议"
            venue = getattr(pub, 'journal', getattr(pub, 'booktitle', 'Unknown'))
            print(f"   {i}. [{pub_type}] {pub.title}")
            print(f"      {venue}, {pub.year}")
        if len(recent_pubs) > 5:
            print(f"   ... 还有 {len(recent_pubs) - 5} 篇近期论文")

    # 主要合作者
    coauthors = profile.get_coauthors()
    if coauthors:
        print_separator()
        print(f"🤝 主要合作者 (显示前10位):")
        for i, coauthor in enumerate(coauthors[:10], 1):
            print(f"   {i}. {coauthor}")
        if len(coauthors) > 10:
            print(f"   ... 还有 {len(coauthors) - 10} 位合作者")

    # 个人主页链接
    if profile.urls:
        print_separator()
        print(f"🔗 相关链接 ({len(profile.urls)}):")
        for i, url in enumerate(profile.urls[:3], 1):
            print(f"   {i}. {url}")
        if len(profile.urls) > 3:
            print(f"   ... 还有 {len(profile.urls) - 3} 个链接")
    
    def run(self) -> None:
        """运行主程序"""
        self.print_header("DBLP 作者信息查询演示程序")
        print("欢迎使用DBLP作者信息查询系统!")
        print("您可以搜索任何作者的姓名，系统将为您展示详细的学术档案信息。")
        print("\n💡 提示:")
        print("  - 支持英文姓名搜索 (如: Geoffrey Hinton)")
        print("  - 支持部分姓名搜索 (如: Hinton)")
        print("  - 输入 'quit' 或 'q' 退出程序")
        
        while True:
            try:
                # 获取用户输入
                query = self.get_user_input()
                
                # 搜索作者
                results = self.search_authors(query)
                
                if not results:
                    print("❌ 未找到匹配的作者，请尝试其他关键词")
                    continue
                
                # 显示搜索结果
                self.display_search_results(results)
                
                # 让用户选择作者
                selected_author = self.select_author(results)
                
                if not selected_author:
                    print("🔄 返回搜索...")
                    continue
                
                # 获取作者详细信息
                profile = self.fetch_author_profile(selected_author)
                
                if not profile:
                    print("❌ 无法获取作者详细信息，请重试")
                    continue
                
                # 显示作者详细信息
                self.display_author_profile(profile)
                
                # 询问是否继续
                while True:
                    continue_choice = input("\n🔄 是否继续搜索其他作者? (y/n): ").strip().lower()
                    if continue_choice in ['y', 'yes', '是', '继续']:
                        break
                    elif continue_choice in ['n', 'no', '否', '退出']:
                        print("👋 感谢使用，再见!")
                        return
                    else:
                        print("⚠️  请输入 y(是) 或 n(否)")
                
            except KeyboardInterrupt:
                print("\n\n👋 程序已中断，再见!")
                break
            except Exception as e:
                print(f"❌ 程序运行出错: {e}")
                print("🔄 正在重新启动...")


def main():
    """主函数"""
    demo = AuthorSearchDemo()
    demo.run()


if __name__ == "__main__":
    main()
